
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { mockTrials } from '@/data/mockData';
import Layout from '@/components/Layout';
import { Plus, Trash2 } from 'lucide-react';

// Mock data for employees and patients
const mockEmployees = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
];

const mockPatients = [
  'P00002',
  'P00004',
  'P00001',
  'P00121',
  'P00221',
  'P00419',
];

interface ProcedureRecord {
  id: string;
  articleNr: string;
  date: string;
  planned: boolean;
}

const TaskReport = () => {
  const [formData, setFormData] = useState({
    trialId: '',
    employeeName: '',
    patientReference: ''
  });

  const [procedures, setProcedures] = useState<ProcedureRecord[]>([
    { id: '1', articleNr: '', date: '', planned: true }
  ]);

  const [employeeSearch, setEmployeeSearch] = useState('');
  const [patientSearch, setPatientSearch] = useState('');
  const [articleSearches, setArticleSearches] = useState<{[key: string]: string}>({});

  const selectedTrial = mockTrials.find(p => p.id === formData.trialId);
  const availableArticles = selectedTrial?.priceList || [];

  const filteredEmployees = mockEmployees.filter(employee =>
    employee.toLowerCase().includes(employeeSearch.toLowerCase())
  );

  const filteredPatients = mockPatients.filter(patient =>
    patient.toLowerCase().includes(patientSearch.toLowerCase())
  );

  const getFilteredArticles = (procedureId: string) => {
    const searchTerm = articleSearches[procedureId] || '';
    return availableArticles.filter(article =>
      article.articleNr.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.articleName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const addProcedure = () => {
    const newId = (procedures.length + 1).toString();
    setProcedures([...procedures, { id: newId, articleNr: '', date: '', planned: true }]);
  };

  const removeProcedure = (id: string) => {
    if (procedures.length > 1) {
      setProcedures(procedures.filter(p => p.id !== id));
      const newSearches = { ...articleSearches };
      delete newSearches[id];
      setArticleSearches(newSearches);
    }
  };

  const updateProcedure = (id: string, field: keyof ProcedureRecord, value: any) => {
    setProcedures(procedures.map(p =>
      p.id === id ? { ...p, [field]: value } : p
    ));
  };

  const updateArticleSearch = (procedureId: string, value: string) => {
    setArticleSearches({ ...articleSearches, [procedureId]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate common fields
    if (!formData.trialId || !formData.employeeName || !formData.patientReference) {
      toast.error('Please fill in Trial, Researcher, and Patient fields');
      return;
    }

    // Validate procedure fields
    const incompleteProcedures = procedures.filter(p => !p.articleNr || !p.date);
    if (incompleteProcedures.length > 0) {
      toast.error('Please fill in all Procedure and Date fields');
      return;
    }

    // Create task records
    const taskRecords = procedures.map(procedure => ({
      ...formData,
      ...procedure
    }));

    console.log('Task records reported:', taskRecords);
    toast.success(`${taskRecords.length} task record(s) reported successfully!`);

    // Reset form
    setFormData({
      trialId: '',
      employeeName: '',
      patientReference: ''
    });
    setProcedures([{ id: '1', articleNr: '', date: '', planned: true }]);
    setEmployeeSearch('');
    setPatientSearch('');
    setArticleSearches({});
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>Report Completed Procedure/Visit</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* First row: Trial, Researcher, Patient */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trials">Trials *</Label>
                  <Select
                    value={formData.trialId}
                    onValueChange={(value) => {
                      setFormData({...formData, trialId: value});
                      setProcedures([{ id: '1', articleNr: '', date: '', planned: true }]);
                      setArticleSearches({});
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select trial" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockTrials.map(trial => (
                        <SelectItem key={trial.id} value={trial.id}>
                          {trial.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employee">Main Researcher *</Label>
                  <Select
                    value={formData.employeeName}
                    onValueChange={(value) => setFormData({...formData, employeeName: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select main researcher" />
                    </SelectTrigger>
                    <SelectContent>
                      <div className="p-2">
                        <Input
                          placeholder="Search..."
                          value={employeeSearch}
                          onChange={(e) => setEmployeeSearch(e.target.value)}
                          className="h-8"
                        />
                      </div>
                      {filteredEmployees.map(employee => (
                        <SelectItem key={employee} value={employee}>
                          {employee}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="patient">Patient *</Label>
                  <Select
                    value={formData.patientReference}
                    onValueChange={(value) => setFormData({...formData, patientReference: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select patient" />
                    </SelectTrigger>
                    <SelectContent>
                      <div className="p-2">
                        <Input
                          placeholder="Search patients..."
                          value={patientSearch}
                          onChange={(e) => setPatientSearch(e.target.value)}
                          className="h-8"
                        />
                      </div>
                      {filteredPatients.map(patient => (
                        <SelectItem key={patient} value={patient}>
                          {patient}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Procedures Section */}
              <div className="space-y-4">
                <Label className="text-base font-semibold">Procedures/Visits *</Label>

                {procedures.map((procedure, index) => (
                  <div key={procedure.id} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">
                        Procedure {index + 1}
                      </span>
                      {procedures.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeProcedure(procedure.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`article-${procedure.id}`}>Procedure/Visit *</Label>
                        <Select
                          value={procedure.articleNr}
                          onValueChange={(value) => updateProcedure(procedure.id, 'articleNr', value)}
                          disabled={!formData.trialId}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose" />
                          </SelectTrigger>
                          <SelectContent>
                            <div className="p-2">
                              <Input
                                placeholder="Search..."
                                value={articleSearches[procedure.id] || ''}
                                onChange={(e) => updateArticleSearch(procedure.id, e.target.value)}
                                className="h-8"
                              />
                            </div>
                            {getFilteredArticles(procedure.id).map(article => (
                              <SelectItem key={article.id} value={article.articleNr}>
                                {article.articleNr} - {article.articleName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`date-${procedure.id}`}>Date *</Label>
                        <Input
                          id={`date-${procedure.id}`}
                          type="date"
                          value={procedure.date}
                          onChange={(e) => updateProcedure(procedure.id, 'date', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`planned-${procedure.id}`}>Status</Label>
                        <div className="flex items-center space-x-2 h-10">
                          <Checkbox
                            id={`planned-${procedure.id}`}
                            checked={procedure.planned}
                            onCheckedChange={(checked) => updateProcedure(procedure.id, 'planned', !!checked)}
                          />
                          <Label htmlFor={`planned-${procedure.id}`}>Planned</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addProcedure}
                  className="flex items-center space-x-2 w-fit"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Procedure</span>
                </Button>
              </div>

              <Button type="submit" className="w-full">
                Report
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default TaskReport;
