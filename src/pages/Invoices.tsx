
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { mockInvoices, mockTrials } from '@/data/mockData';
import Layout from '@/components/Layout';
import { FileText, Download } from 'lucide-react';
import { toast } from 'sonner';

const Invoices = () => {
  const [trialFilter, setTrialFilter] = useState<string>('all');

  const filteredInvoices = mockInvoices.filter(invoice => {
    if (trialFilter === 'all') return true;
    return invoice.tasks.some(task => task.trialId === trialFilter);
  });

  const handleDownloadInvoice = (invoiceId: string) => {
    console.log('Downloading invoice:', invoiceId);
    toast.success('Invoice downloaded successfully');
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Invoices</h1>
        </div>

        {/* Filters */}
        <Card>
          <CardContent>
            <div className="grid pt-4 grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Trial</Label>
                <Select
                  value={trialFilter}
                  onValueChange={setTrialFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All trials" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All trials</SelectItem>
                    {mockTrials.map(trial => (
                      <SelectItem key={trial.id} value={trial.id}>
                        {trial.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-4">
          {filteredInvoices.map(invoice => (
            <Card key={invoice.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{invoice.invoiceNumber}</h3>
                      <p className="text-sm text-gray-600">Date: {invoice.date}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        €{invoice.totalAmount.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Total Amount</div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownloadInvoice(invoice.id)}
                      className="flex items-center space-x-2"
                    >
                      <Download className="h-4 w-4" />
                      <span>Download</span>
                    </Button>
                  </div>
                </div>

                <div className="mt-4">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b text-sm">
                          <th className="text-left p-2 font-medium">Date</th>
                          <th className="text-left p-2 font-medium">Main Researcher</th>
                          <th className="text-right p-2 font-medium">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoice.tasks.map(task => (
                          <tr key={task.id} className="border-b last:border-b-0">
                            <td className="p-2 text-sm">{task.date}</td>
                            <td className="p-2 text-sm">{task.employeeName}</td>
                            <td className="p-2 text-sm text-right font-medium">
                              €{Math.round(invoice.totalAmount / invoice.tasks.length).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredInvoices.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Invoices Found</h3>
                <p className="text-gray-500">
                  {trialFilter === 'all'
                    ? "No invoices have been created yet."
                    : "No invoices found for the selected trial."
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Invoices;
