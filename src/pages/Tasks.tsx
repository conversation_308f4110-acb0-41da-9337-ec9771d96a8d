
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { mockTasks, mockTrials } from '@/data/mockData';
import { Task, TaskFilters } from '@/types';
import Layout from '@/components/Layout';
import { Download, Check, Receipt, X, Trash2, Edit } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

// Mock data for employees
const mockEmployees = [
  'Alice Cooper',
  'Bob Wilson',
  'Charlie Brown',
  'David Smith',
  'Eva Johnson',
  'Frank Miller',
  'Grace Davis',
  'Henry Wilson'
];

// Mock data for patients
const mockPatients = [
  'P00001',
  'P00002',
  'P00004',
  'P00005',
  'P00121',
  'P00221',
  'P00419'
];

const Tasks = () => {
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [filters, setFilters] = useState<TaskFilters>({});
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [editFormData, setEditFormData] = useState({
    date: '',
    employeeName: '',
    planned: true,
    articleNr: '',
    patientReference: ''
  });

  const filteredTasks = tasks.filter(task => {
    if (filters.patientReference && !task.patientReference.toLowerCase().includes(filters.patientReference.toLowerCase())) return false;
    if (filters.trialId && task.trialId !== filters.trialId) return false;
    if (filters.articleNr && !task.articleNr.toLowerCase().includes(filters.articleNr.toLowerCase())) return false;
    if (filters.dateFrom && task.date < filters.dateFrom) return false;
    if (filters.dateTo && task.date > filters.dateTo) return false;
    if (filters.confirmed !== undefined && task.confirmed !== filters.confirmed) return false;
    if (filters.invoiced !== undefined && task.invoiced !== filters.invoiced) return false;
    if (filters.planned !== undefined && task.planned !== filters.planned) return false;
    return true;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTasks(filteredTasks.filter(t => !t.deleted).map(t => t.id));
    } else {
      setSelectedTasks([]);
    }
  };

  const handleSelectTask = (taskId: string, checked: boolean) => {
    if (checked) {
      setSelectedTasks([...selectedTasks, taskId]);
    } else {
      setSelectedTasks(selectedTasks.filter(id => id !== taskId));
    }
  };

  // Check if any selected tasks can be confirmed (unconfirmed tasks)
  const canConfirmSelected = () => {
    return selectedTasks.some(taskId => {
      const task = tasks.find(t => t.id === taskId);
      return task && !task.confirmed && !task.deleted;
    });
  };

  // Check if any selected tasks can be invoiced (confirmed but not invoiced tasks)
  const canInvoiceSelected = () => {
    return selectedTasks.some(taskId => {
      const task = tasks.find(t => t.id === taskId);
      return task && task.confirmed && !task.invoiced && !task.deleted;
    });
  };

  const handleConfirmSelected = () => {
    const selectedUnconfirmedTasks = tasks.filter(task =>
      selectedTasks.includes(task.id) && !task.confirmed && !task.deleted
    );

    if (selectedUnconfirmedTasks.length === 0) {
      toast.error('Please select unconfirmed tasks');
      return;
    }

    setTasks(tasks.map(task =>
      selectedTasks.includes(task.id) && !task.confirmed && !task.deleted
        ? { ...task, confirmed: true }
        : task
    ));
    setSelectedTasks([]);
    toast.success(`${selectedUnconfirmedTasks.length} tasks confirmed`);
  };

  const handleInvoiceSelected = () => {
    const selectedTasksData = tasks.filter(task =>
      selectedTasks.includes(task.id) && task.confirmed && !task.invoiced && !task.deleted
    );

    if (selectedTasksData.length === 0) {
      toast.error('Please select confirmed, non-invoiced tasks');
      return;
    }

    setTasks(tasks.map(task =>
      selectedTasks.includes(task.id) && task.confirmed && !task.deleted
        ? { ...task, invoiced: true }
        : task
    ));
    setSelectedTasks([]);
    toast.success(`${selectedTasksData.length} tasks marked as invoiced`);
  };

  const handleDeleteTask = (taskId: string) => {
    setTasks(tasks.map(t =>
      t.id === taskId ? { ...t, deleted: true } : t
    ));
    toast.success('Task marked as deleted');
  };

  const handleEditTask = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setEditingTask(task);
      setEditFormData({
        date: task.date,
        employeeName: task.employeeName,
        planned: task.planned,
        articleNr: task.articleNr,
        patientReference: task.patientReference
      });
      setEditDialogOpen(true);
    }
  };

  const handleSaveEdit = () => {
    if (!editingTask) return;

    if (!editFormData.date || !editFormData.employeeName || !editFormData.articleNr || !editFormData.patientReference) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Find the selected trial and article to get the article name
    const selectedTrial = mockTrials.find(trial => trial.id === editingTask.trialId);
    const selectedArticle = selectedTrial?.priceList.find(article => article.articleNr === editFormData.articleNr);

    const updatedTasks = tasks.map(task =>
      task.id === editingTask.id
        ? {
            ...task,
            date: editFormData.date,
            employeeName: editFormData.employeeName,
            planned: editFormData.planned,
            articleNr: editFormData.articleNr,
            articleName: selectedArticle?.articleName || task.articleName,
            patientReference: editFormData.patientReference
          }
        : task
    );

    setTasks(updatedTasks);
    setEditDialogOpen(false);
    setEditingTask(null);
    toast.success('Task updated successfully');
  };

  const handleCancelEdit = () => {
    setEditDialogOpen(false);
    setEditingTask(null);
    setEditFormData({
      date: '',
      employeeName: '',
      planned: true,
      articleNr: '',
      patientReference: ''
    });
  };

  const handleExportSelected = () => {
    const selectedTasksData = tasks.filter(task =>
      selectedTasks.includes(task.id) && !task.deleted
    );

    if (selectedTasksData.length === 0) {
      toast.error('Please select tasks to export');
      return;
    }

    console.log('Exporting selected tasks:', selectedTasksData);
    toast.success(`${selectedTasksData.length} tasks exported`);
  };

  const handleExportSelectedNonCompleted = () => {
    const selectedNonCompletedTasks = tasks.filter(task =>
      selectedTasks.includes(task.id) && !task.confirmed && !task.deleted
    );

    if (selectedNonCompletedTasks.length === 0) {
      toast.error('Please select non-completed tasks');
      return;
    }

    console.log('Exporting selected non-completed tasks:', selectedNonCompletedTasks);
    toast.success(`${selectedNonCompletedTasks.length} non-completed tasks exported`);
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Tasks</h1>
        </div>

        {/* Filters */}
        <Card>
          <CardContent>
            <div className="grid pt-4 grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
              <div className="space-y-1">
                <Label className="text-xs">Patient Ref</Label>
                <Input
                  placeholder="Filter..."
                  className="h-8 text-sm"
                  value={filters.patientReference || ''}
                  onChange={(e) => setFilters({...filters, patientReference: e.target.value})}
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Trial</Label>
                <Select
                  value={filters.trialId || 'all'}
                  onValueChange={(value) => setFilters({...filters, trialId: value === 'all' ? undefined : value})}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All trials</SelectItem>
                    {mockTrials.map(trial => (
                      <SelectItem key={trial.id} value={trial.id}>
                        {trial.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Article Nr</Label>
                <Input
                  placeholder="Filter..."
                  className="h-8 text-sm"
                  value={filters.articleNr || ''}
                  onChange={(e) => setFilters({...filters, articleNr: e.target.value})}
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Confirmed</Label>
                <Select
                  value={
                    filters.confirmed === true ? 'yes' :
                    filters.confirmed === false ? 'no' : 'all'
                  }
                  onValueChange={(value) => {
                    const newFilters = { ...filters };
                    if (value === 'yes') newFilters.confirmed = true;
                    else if (value === 'no') newFilters.confirmed = false;
                    else delete newFilters.confirmed;
                    setFilters(newFilters);
                  }}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Invoiced</Label>
                <Select
                  value={
                    filters.invoiced === true ? 'yes' :
                    filters.invoiced === false ? 'no' : 'all'
                  }
                  onValueChange={(value) => {
                    const newFilters = { ...filters };
                    if (value === 'yes') newFilters.invoiced = true;
                    else if (value === 'no') newFilters.invoiced = false;
                    else delete newFilters.invoiced;
                    setFilters(newFilters);
                  }}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Planned</Label>
                <Select
                  value={
                    filters.planned === true ? 'planned' :
                    filters.planned === false ? 'unplanned' : 'all'
                  }
                  onValueChange={(value) => {
                    const newFilters = { ...filters };
                    if (value === 'planned') newFilters.planned = true;
                    else if (value === 'unplanned') newFilters.planned = false;
                    else delete newFilters.planned;
                    setFilters(newFilters);
                  }}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="unplanned">Unplanned</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Date From</Label>
                <Input
                  type="date"
                  className="h-8 text-sm"
                  value={filters.dateFrom || ''}
                  onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs">Date To</Label>
                <Input
                  type="date"
                  className="h-8 text-sm"
                  value={filters.dateTo || ''}
                  onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedTasks.length > 0 && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {selectedTasks.length} tasks selected
                </span>
                <div className="flex space-x-2">
                  {canConfirmSelected() && (
                    <Button size="sm" onClick={handleConfirmSelected} className="flex items-center space-x-2">
                      <Check className="h-4 w-4" />
                      <span>Confirm Selected</span>
                    </Button>
                  )}
                  {canInvoiceSelected() && (
                    <Button size="sm" onClick={handleInvoiceSelected} className="flex items-center space-x-2">
                      <Receipt className="h-4 w-4" />
                      <span>Invoice Selected</span>
                    </Button>
                  )}
                  <Button size="sm" onClick={handleExportSelected} variant="outline" className="flex items-center space-x-2">
                    <Download className="h-4 w-4" />
                    <span>Export Selected</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tasks Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedTasks.length === filteredTasks.filter(t => !t.deleted).length && filteredTasks.length > 0}
                  onCheckedChange={handleSelectAll}
                />
                <Label className="text-sm">Select All ({filteredTasks.length})</Label>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Select</th>
                    <th className="text-left p-2">Trial</th>
                    <th className="text-left p-2">Article</th>
                    <th className="text-left p-2">Patient</th>
                    <th className="text-left p-2">Main Researcher</th>
                    <th className="text-left p-2">Date</th>
                    <th className="text-left p-2">Confirmed</th>
                    <th className="text-left p-2">Invoiced</th>
                    <th className="text-left p-2">Planned</th>
                    <th className="text-left p-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTasks.map(task => (
                    <tr
                      key={task.id}
                      className={`border-b ${
                        task.deleted ? 'line-through opacity-50' :
                        task.confirmed ? 'bg-green-50' : ''
                      }`}
                    >
                      <td className="p-2">
                        {!task.deleted && (
                          <Checkbox
                            checked={selectedTasks.includes(task.id)}
                            onCheckedChange={(checked) => handleSelectTask(task.id, !!checked)}
                          />
                        )}
                      </td>
                      <td className="p-2">{task.trialName}</td>
                      <td className="p-2">
                        <div>
                          <div className="font-mono text-sm">{task.articleNr}</div>
                          <div className="text-sm text-gray-600">{task.articleName}</div>
                        </div>
                      </td>
                      <td className="p-2">{task.patientReference}</td>
                      <td className="p-2">{task.employeeName}</td>
                      <td className="p-2">{task.date}</td>
                      <td className="p-2">
                        {task.confirmed ? (
                          <Check className="h-5 w-5 text-green-600" />
                        ) : (
                          <X className="h-5 w-5 text-red-600" />
                        )}
                      </td>
                      <td className="p-2">
                        {task.invoiced ? (
                          <Check className="h-5 w-5 text-green-600" />
                        ) : (
                          <X className="h-5 w-5 text-red-600" />
                        )}
                      </td>
                      <td className="p-2">
                        <Badge variant={task.planned ? "outline" : "destructive"}>
                          {task.planned ? "Planned" : "Unplanned"}
                        </Badge>
                      </td>
                      <td className="p-2">
                        {!task.deleted && (
                          <div className="flex items-center space-x-2">
                            <Button
                              size="icon"
                              variant="outline"
                              onClick={() => handleEditTask(task.id)}
                              disabled={task.confirmed}
                              className="h-8 w-8"
                              title={task.confirmed ? "Cannot edit confirmed tasks" : "Edit task"}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="destructive"
                                  disabled={task.confirmed}
                                  className="h-8 w-8"
                                  title={task.confirmed ? "Cannot delete confirmed tasks" : "Delete task"}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Task</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this task? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <div className="my-4 p-4 bg-gray-50 rounded-lg">
                                  <div className="space-y-2 text-sm">
                                    <div><strong>Trial:</strong> {task.trialName}</div>
                                    <div><strong>Procedure:</strong> {task.articleNr} - {task.articleName}</div>
                                    <div><strong>Patient:</strong> {task.patientReference}</div>
                                    <div><strong>Date:</strong> {task.date}</div>
                                    <div><strong>Researcher:</strong> {task.employeeName}</div>
                                  </div>
                                </div>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteTask(task.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete Task
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit Task Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          {editingTask && (
            <div className="space-y-4">
              {/* Task Info (Read-only) */}
              <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                <div className="text-sm">
                  <strong>Trial:</strong> {editingTask.trialName}
                </div>
              </div>

              {/* Editable Fields */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-procedure">Procedure/Visit *</Label>
                  <Select
                    value={editFormData.articleNr}
                    onValueChange={(value) => setEditFormData({...editFormData, articleNr: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select procedure" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockTrials.find(trial => trial.id === editingTask.trialId)?.priceList.map(article => (
                        <SelectItem key={article.id} value={article.articleNr}>
                          {article.articleNr} - {article.articleName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-patient">Patient *</Label>
                  <Select
                    value={editFormData.patientReference}
                    onValueChange={(value) => setEditFormData({...editFormData, patientReference: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select patient" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockPatients.map(patient => (
                        <SelectItem key={patient} value={patient}>
                          {patient}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-employee">Main Researcher *</Label>
                  <Select
                    value={editFormData.employeeName}
                    onValueChange={(value) => setEditFormData({...editFormData, employeeName: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select main researcher" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockEmployees.map(employee => (
                        <SelectItem key={employee} value={employee}>
                          {employee}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-date">Date *</Label>
                  <Input
                    id="edit-date"
                    type="date"
                    value={editFormData.date}
                    onChange={(e) => setEditFormData({...editFormData, date: e.target.value})}
                    className="[&::-webkit-calendar-picker-indicator]:ml-auto [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-planned"
                    checked={editFormData.planned}
                    onCheckedChange={(checked) => setEditFormData({...editFormData, planned: !!checked})}
                  />
                  <Label htmlFor="edit-planned">Planned</Label>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Tasks;
