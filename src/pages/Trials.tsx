import React, { useState } from 'react';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { mockTrials } from '@/data/mockData';
import { Trial } from '@/types';
import Layout from '@/components/Layout';
import CreateTrialDialog from '@/components/CreateTrialDialog';
import { Edit, Users, Euro, Building } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const Trials = () => {
  const [trials, setTrials] = useState<Trial[]>(mockTrials);
  const navigate = useNavigate();

  const handleEditPriceList = (trial: Trial) => {
    navigate(`/trials/${trial.id}/price-list`);
  };

  const handleCreateTrial = (newTrial: Trial) => {
    setTrials([...trials, newTrial]);
  };

  const getTotalTrialValue = (trial: Trial) => {
    return trial.priceList.reduce((total, item) => total + item.totalPrice, 0);
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Trials</h1>
          <CreateTrialDialog onCreateTrial={handleCreateTrial} />
        </div>

        <div className="grid gap-6">
          {trials.map(trial => (
            <Card key={trial.id}>
              <CardHeader>
                <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-center">
                  {/* Trial information column */}
                  <div className="space-y-2">
                    <CardTitle className="text-xl">{trial.name}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>Sponsor: {trial.sponsor}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Building className="h-4 w-4" />
                        <span>Patients: {trial.patients.length}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Euro className="h-4 w-4" />
                        <span>Total: €{getTotalTrialValue(trial).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions column */}
                  <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2">
                    <Badge variant="outline">{trial.priceList.length} Articles</Badge>
                    <Button
                      size="sm"
                      onClick={() => handleEditPriceList(trial)}
                      className="flex items-center space-x-2 w-full sm:w-auto"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Edit Price List</span>
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => navigate(`/trials/${trial.id}/patients`)}
                      className="flex items-center space-x-2 w-full sm:w-auto"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Edit Patients</span>
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default Trials;
