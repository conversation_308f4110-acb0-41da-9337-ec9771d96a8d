
import { Trial, Task, Invoice, Employee, Role } from '@/types';

export const mockTrials: Trial[] = [
  {
    id: '1',
    name: 'Cardiovascular Disease Prevention Study',
    sponsor: 'Dr. <PERSON>',
    patients: [
      { id: '1', name: 'P000123' },
      { id: '2', name: 'P000421' }
    ],
    priceList: [
      {
        id: '1',
        articleNr: 'A-0001',
        articleName: 'Initial Visit',
        totalPrice: 4500, // EUR
        vendorSplit: [
          { vendorName: 'Main Researcher', price: 2700 },
          { vendorName: 'Hospital 1', price: 1800 }
        ]
      },
      {
        id: '2',
        articleNr: 'A-0002',
        articleName: 'Follow-up Visit',
        totalPrice: 3200, // EUR
        vendorSplit: [
          { vendorName: 'Hospital A', price: 3200 }
        ]
      }
    ]
  },
  {
    id: '2',
    name: 'Diabetes Management Clinical Trial',
    sponsor: 'Dr. <PERSON>',
    patients: [
      { id: '3', name: 'P00001' },
      { id: '4', name: 'P00002' },
      { id: '5', name: 'P00005' }
    ],
    priceList: [
      {
        id: '3',
        articleNr: 'A-0003',
        articleName: 'Screening Visit',
        totalPrice: 7200, // EUR
        vendorSplit: [
          { vendorName: 'Main Researcher', price: 7200 }
        ]
      },
      {
        id: '4',
        articleNr: 'A-0004',
        articleName: 'Treatment Visit',
        totalPrice: 6800, // EUR
        vendorSplit: [
          { vendorName: 'Hospital 1', price: 4000 },
          { vendorName: 'Doctor A', price: 2800 }
        ]
      }
    ]
  }
];

export const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Administrator',
    permissions: {
      tasks: { create: true, view: true, edit: true, delete: true, confirm: true },
      invoices: { view: true, create: true },
      trials: { create: true, edit: true },
      employees: { edit: true, create: true },
      sponsors: { edit: true, create: true }
    }
  },
  {
    id: '2',
    name: 'Trial Manager',
    permissions: {
      tasks: { create: true, view: true, edit: true, delete: false, confirm: true },
      invoices: { view: true, create: true },
      trials: { create: true, edit: true },
      employees: { edit: false, create: false },
      sponsors: { edit: false, create: false }
    }
  },
  {
    id: '3',
    name: 'Employee',
    permissions: {
      tasks: { create: true, view: true, edit: false, delete: false, confirm: false },
      invoices: { view: false, create: false },
      trials: { create: false, edit: false },
      employees: { edit: false, create: false },
      sponsors: { edit: false, create: false }
    }
  }
];

export const mockEmployees: Employee[] = [
  { id: '1', name: 'John Doe', roleId: '1' },
  { id: '2', name: 'Jane Smith', roleId: '2' },
  { id: '3', name: 'Mike Johnson', roleId: '3' },
];

export const mockTasks: Task[] = [
  // First 10 tasks - NOT CONFIRMED (IDs 1-10)
  {
    id: '1',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00419',
    employeeName: 'Grace Davis',
    date: '2024-06-25',
    confirmed: false,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '2',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00002',
    employeeName: 'Bob Wilson',
    date: '2024-06-24',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '3',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00121',
    employeeName: 'David Smith',
    date: '2024-06-23',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '4',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00001',
    employeeName: 'Eva Johnson',
    date: '2024-06-22',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '5',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00221',
    employeeName: 'Frank Miller',
    date: '2024-06-21',
    confirmed: false,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '6',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00004',
    employeeName: 'Charlie Brown',
    date: '2024-06-20',
    confirmed: false,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '7',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00005',
    employeeName: 'Henry Wilson',
    date: '2024-06-19',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '8',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00002',
    employeeName: 'Alice Cooper',
    date: '2024-06-18',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '9',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00121',
    employeeName: 'Bob Wilson',
    date: '2024-06-17',
    confirmed: false,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '10',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00419',
    employeeName: 'Grace Davis',
    date: '2024-06-16',
    confirmed: false,
    invoiced: false,
    planned: false,
    deleted: false
  },

  // Next 10 tasks - CONFIRMED but NOT INVOICED (IDs 11-20)
  {
    id: '11',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00001',
    employeeName: 'David Smith',
    date: '2024-06-15',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '12',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00221',
    employeeName: 'Eva Johnson',
    date: '2024-06-14',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '13',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00004',
    employeeName: 'Frank Miller',
    date: '2024-06-13',
    confirmed: true,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '14',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00002',
    employeeName: 'Charlie Brown',
    date: '2024-06-12',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '15',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00005',
    employeeName: 'Henry Wilson',
    date: '2024-06-11',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '16',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00121',
    employeeName: 'Alice Cooper',
    date: '2024-06-10',
    confirmed: true,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '17',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00419',
    employeeName: 'Bob Wilson',
    date: '2024-06-09',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '18',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00001',
    employeeName: 'Grace Davis',
    date: '2024-06-08',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },
  {
    id: '19',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00221',
    employeeName: 'David Smith',
    date: '2024-06-07',
    confirmed: true,
    invoiced: false,
    planned: false,
    deleted: false
  },
  {
    id: '20',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00004',
    employeeName: 'Eva Johnson',
    date: '2024-06-06',
    confirmed: true,
    invoiced: false,
    planned: true,
    deleted: false
  },

  // Last 10 tasks - CONFIRMED and INVOICED (IDs 21-30)
  {
    id: '21',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00002',
    employeeName: 'Frank Miller',
    date: '2024-06-05',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '22',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00121',
    employeeName: 'Charlie Brown',
    date: '2024-06-04',
    confirmed: true,
    invoiced: true,
    planned: false,
    deleted: false
  },
  {
    id: '23',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00419',
    employeeName: 'Henry Wilson',
    date: '2024-06-03',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '24',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00005',
    employeeName: 'Alice Cooper',
    date: '2024-06-02',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '25',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00001',
    employeeName: 'Bob Wilson',
    date: '2024-06-01',
    confirmed: true,
    invoiced: true,
    planned: false,
    deleted: false
  },
  {
    id: '26',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00221',
    employeeName: 'Grace Davis',
    date: '2024-05-31',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '27',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0001',
    articleName: 'Initial Visit',
    patientReference: 'P00004',
    employeeName: 'David Smith',
    date: '2024-05-30',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '28',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0004',
    articleName: 'Treatment Visit',
    patientReference: 'P00002',
    employeeName: 'Eva Johnson',
    date: '2024-05-29',
    confirmed: true,
    invoiced: true,
    planned: false,
    deleted: false
  },
  {
    id: '29',
    trialId: '1',
    trialName: 'Cardiovascular Disease Prevention Study',
    articleNr: 'A-0002',
    articleName: 'Follow-up Visit',
    patientReference: 'P00121',
    employeeName: 'Frank Miller',
    date: '2024-05-28',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  },
  {
    id: '30',
    trialId: '2',
    trialName: 'Diabetes Management Clinical Trial',
    articleNr: 'A-0003',
    articleName: 'Screening Visit',
    patientReference: 'P00419',
    employeeName: 'Charlie Brown',
    date: '2024-05-27',
    confirmed: true,
    invoiced: true,
    planned: true,
    deleted: false
  }
];

export const mockInvoices: Invoice[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    date: '2024-06-11',
    tasks: [mockTasks[2]],
    totalAmount: 7200, // EUR
    status: 'sent'
  }
];
